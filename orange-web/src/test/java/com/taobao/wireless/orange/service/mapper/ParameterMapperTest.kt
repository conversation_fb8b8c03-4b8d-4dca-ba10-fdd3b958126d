package com.taobao.wireless.orange.service.mapper

import com.taobao.wireless.orange.BaseTest
import com.taobao.wireless.orange.common.constant.enums.*
import com.taobao.wireless.orange.dal.enhanced.entity.OConditionDO
import com.taobao.wireless.orange.dal.enhanced.entity.OParameterConditionVersionDO
import com.taobao.wireless.orange.dal.enhanced.entity.OParameterVersionDO
import com.taobao.wireless.orange.dal.enhanced.entity.OReleaseOrderDO
import com.taobao.wireless.orange.manager.model.ParameterBO
import com.taobao.wireless.orange.service.model.ParameterConditionDTO
import com.taobao.wireless.orange.service.model.ParameterDetailDTO
import com.taobao.wireless.orange.service.model.ReleaseOrderDTO
import org.assertj.core.api.Assertions
import org.assertj.core.api.ThrowingConsumer
import org.junit.Before
import org.junit.Test
import org.springframework.beans.factory.annotation.Autowired

/**
 * ParameterMapper单元测试
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
class ParameterMapperTest : BaseTest() {
    @Autowired
    private val parameterMapper: ParameterMapper? = null

    private var testParameterBO: ParameterBO? = null
    private var testConditionMap: MutableMap<String?, OConditionDO?>? = null
    private var testReleaseOrderDO: OReleaseOrderDO? = null

    @Before
    fun setUp() {
        setupTestData()
    }

    private fun setupTestData() {
        // 创建测试用的ParameterBO
        testParameterBO = ParameterBO()
        testParameterBO!!.setParameterId(generateTestId())
        testParameterBO!!.setParameterKey("test_parameter")
        testParameterBO!!.setAppKey(DEFAULT_APP_KEY)
        testParameterBO!!.setNamespaceId(generateTestId())
        testParameterBO!!.setDescription("测试参数描述")
        testParameterBO!!.setValueType(ParameterValueType.JSON)
        testParameterBO!!.setStatus(ParameterStatus.ONLINE)
        testParameterBO!!.setGmtCreate(getCurrentTime())
        testParameterBO!!.setGmtModified(getCurrentTime())
        testParameterBO!!.setCreator(DEFAULT_WORKER_ID)
        testParameterBO!!.setModifier(DEFAULT_WORKER_ID)

        // 创建测试用的ParameterVersion
        val parameterVersion = OParameterVersionDO()
        parameterVersion.setId(1L)
        parameterVersion.setParameterId(testParameterBO!!.getParameterId())
        parameterVersion.setReleaseVersion("v1.0.0")
        parameterVersion.setConditionsOrder("condition1,condition2")
        parameterVersion.setPreviousReleaseVersion("v0.9.0")
        parameterVersion.setChangeType(ChangeType.UPDATE)
        parameterVersion.setStatus(VersionStatus.INIT)
        parameterVersion.setGmtCreate(getCurrentTime())
        parameterVersion.setGmtModified(getCurrentTime())
        parameterVersion.setCreator(DEFAULT_WORKER_ID)
        parameterVersion.setModifier(DEFAULT_WORKER_ID)
        testParameterBO!!.setParameterVersion(parameterVersion)

        // 创建测试用的ParameterConditionVersions
        val conditionVersions: MutableList<OParameterConditionVersionDO?> = ArrayList<OParameterConditionVersionDO?>()


        // 普通条件
        val conditionVersion1 = OParameterConditionVersionDO()
        conditionVersion1.setId(1L)
        conditionVersion1.setParameterId(testParameterBO!!.getParameterId())
        conditionVersion1.setConditionId("condition1")
        conditionVersion1.setReleaseVersion("v1.0.0")
        conditionVersion1.setAppKey(DEFAULT_APP_KEY)
        conditionVersion1.setNamespaceId(testParameterBO!!.getNamespaceId())
        conditionVersion1.setValue("{\"condition_value\":\"test1\"}")
        conditionVersion1.setStatus(VersionStatus.INIT)
        conditionVersion1.setGmtCreate(getCurrentTime())
        conditionVersion1.setGmtModified(getCurrentTime())
        conditionVersion1.setCreator(DEFAULT_WORKER_ID)
        conditionVersion1.setModifier(DEFAULT_WORKER_ID)
        conditionVersions.add(conditionVersion1)

        // 默认条件
        val defaultCondition = OParameterConditionVersionDO()
        defaultCondition.setId(2L)
        defaultCondition.setParameterId(testParameterBO!!.getParameterId())
        defaultCondition.setConditionId(ParameterMapper.DEFAULT_CONDITION_ID)
        defaultCondition.setReleaseVersion("v1.0.0")
        defaultCondition.setAppKey(DEFAULT_APP_KEY)
        defaultCondition.setNamespaceId(testParameterBO!!.getNamespaceId())
        defaultCondition.setValue("{\"default_value\":\"test_default\"}")
        defaultCondition.setStatus(VersionStatus.INIT)
        defaultCondition.setGmtCreate(getCurrentTime())
        defaultCondition.setGmtModified(getCurrentTime())
        defaultCondition.setCreator(DEFAULT_WORKER_ID)
        defaultCondition.setModifier(DEFAULT_WORKER_ID)
        conditionVersions.add(defaultCondition)

        testParameterBO!!.setParameterConditionVersions(conditionVersions)

        // 创建测试用的条件映射
        testConditionMap = HashMap<String?, OConditionDO?>()
        val condition1 = OConditionDO()
        condition1.setConditionId("condition1")
        condition1.setName("测试条件1")
        condition1.setColor("#FF0000")
        condition1.setStatus(ConditionStatus.INIT)
        testConditionMap!!.put("condition1", condition1)

        // 创建测试用的ReleaseOrder
        testReleaseOrderDO = OReleaseOrderDO()
        testReleaseOrderDO!!.setId(1L)
        testReleaseOrderDO!!.setReleaseVersion("v1.0.0")
        testReleaseOrderDO!!.setAppKey(DEFAULT_APP_KEY)
        testReleaseOrderDO!!.setNamespaceId(generateTestId())
        testReleaseOrderDO!!.setBizType(ReleaseOrderBizType.NAMESPACE)
        testReleaseOrderDO!!.setBizId(testReleaseOrderDO!!.getNamespaceId())
        testReleaseOrderDO!!.setReleaseType(ReleaseType.PUBLISH)
        testReleaseOrderDO!!.setDescription("测试发布单")
        testReleaseOrderDO!!.setStatus(ReleaseOrderStatus.INIT)
        testReleaseOrderDO!!.setPercent(10000)
        testReleaseOrderDO!!.setGmtCreate(getCurrentTime())
        testReleaseOrderDO!!.setGmtModified(getCurrentTime())
        testReleaseOrderDO!!.setCreator(DEFAULT_WORKER_ID)
        testReleaseOrderDO!!.setModifier(DEFAULT_WORKER_ID)

        testParameterBO!!.setInPublishReleaseOrder(testReleaseOrderDO)
    }

    @Test
    fun testToParameterDetailDTO() {
        // 执行转换
        val result = parameterMapper!!.toParameterDetailDTO(testParameterBO, testConditionMap)

        // 验证基本字段
        Assertions.assertThat<ParameterDetailDTO?>(result).isNotNull()
        Assertions.assertThat(result!!.getParameterId()).isEqualTo(testParameterBO!!.getParameterId())
        Assertions.assertThat(result.getParameterKey()).isEqualTo(testParameterBO!!.getParameterKey())
        Assertions.assertThat(result.getDescription()).isEqualTo(testParameterBO!!.getDescription())
        Assertions.assertThat<ParameterValueType?>(result.getValueType()).isEqualTo(testParameterBO!!.getValueType())
        Assertions.assertThat<ParameterStatus?>(result.getStatus()).isEqualTo(testParameterBO!!.getStatus())

        // 验证从ParameterVersion映射的字段
        Assertions.assertThat(result.getReleaseVersion()).isEqualTo("v1.0.0")
        Assertions.assertThat(result.getConditionsOrder()).isEqualTo("condition1,condition2")
        Assertions.assertThat(result.getPreviousReleaseVersion()).isEqualTo("v0.9.0")

        // 验证参数条件列表
        Assertions.assertThat<ParameterConditionDTO?>(result.getParameterConditions()).hasSize(2)


        // 验证普通条件
        val condition1 = result.getParameterConditions().stream()
            .filter { c: ParameterConditionDTO? -> "condition1" == c!!.getConditionId() }
            .findFirst()
            .orElse(null)
        Assertions.assertThat<ParameterConditionDTO?>(condition1).isNotNull()
        Assertions.assertThat(condition1!!.getConditionName()).isEqualTo("测试条件1")
        Assertions.assertThat(condition1.getConditionColor()).isEqualTo("#FF0000")
        Assertions.assertThat(condition1.getValue()).isEqualTo("{\"condition_value\":\"test1\"}")

        // 验证默认条件
        val defaultCondition = result.getParameterConditions().stream()
            .filter { c: ParameterConditionDTO? -> ParameterMapper.DEFAULT_CONDITION_ID == c!!.getConditionId() }
            .findFirst()
            .orElse(null)
        Assertions.assertThat<ParameterConditionDTO?>(defaultCondition).isNotNull()
        Assertions.assertThat(defaultCondition!!.getConditionName()).isEqualTo(ParameterMapper.DEFAULT_CONDITION_NAME)
        Assertions.assertThat(defaultCondition.getConditionColor()).isNull()
        Assertions.assertThat(defaultCondition.getValue()).isEqualTo("{\"default_value\":\"test_default\"}")

        // 验证发布单信息
        Assertions.assertThat<ReleaseOrderDTO?>(result.getInPublishReleaseOrder()).isNotNull()
        Assertions.assertThat(result.getInPublishReleaseOrder().getReleaseVersion()).isEqualTo("v1.0.0")
        Assertions.assertThat<ReleaseOrderStatus?>(result.getInPublishReleaseOrder().getStatus())
            .isEqualTo(ReleaseOrderStatus.INIT)
    }

    @Test
    fun testToParameterDetailDTO_WithNullConditionVersions() {
        // 设置空的条件版本列表
        testParameterBO!!.setParameterConditionVersions(null)

        // 执行转换
        val result = parameterMapper!!.toParameterDetailDTO(testParameterBO, testConditionMap)

        // 验证结果
        Assertions.assertThat<ParameterDetailDTO?>(result).isNotNull()
        Assertions.assertThat<ParameterConditionDTO?>(result!!.getParameterConditions()).isNull()
    }

    @Test
    fun testToParameterDetailDTO_WithEmptyConditionMap() {
        // 使用空的条件映射
        val emptyConditionMap: MutableMap<String?, OConditionDO?> = HashMap<String?, OConditionDO?>()

        // 执行转换
        val result = parameterMapper!!.toParameterDetailDTO(testParameterBO, emptyConditionMap)

        // 验证结果
        Assertions.assertThat<ParameterDetailDTO?>(result).isNotNull()
        Assertions.assertThat<ParameterConditionDTO?>(result!!.getParameterConditions()).hasSize(2)


        // 验证普通条件（找不到条件信息）
        val condition1 = result.getParameterConditions().stream()
            .filter { c: ParameterConditionDTO? -> "condition1" == c!!.getConditionId() }
            .findFirst()
            .orElse(null)
        Assertions.assertThat<ParameterConditionDTO?>(condition1).isNotNull()
        Assertions.assertThat(condition1!!.getConditionName()).isNull()
        Assertions.assertThat(condition1.getConditionColor()).isNull()

        // 验证默认条件（使用默认名称）
        val defaultCondition = result.getParameterConditions().stream()
            .filter { c: ParameterConditionDTO? -> ParameterMapper.DEFAULT_CONDITION_ID == c!!.getConditionId() }
            .findFirst()
            .orElse(null)
        Assertions.assertThat<ParameterConditionDTO?>(defaultCondition).isNotNull()
        Assertions.assertThat(defaultCondition!!.getConditionName()).isEqualTo(ParameterMapper.DEFAULT_CONDITION_NAME)
        Assertions.assertThat(defaultCondition.getConditionColor()).isNull()
    }

    @Test
    fun testToReleaseOrderDTO() {
        // 执行转换
        val result = parameterMapper!!.toReleaseOrderDTO(testReleaseOrderDO)

        // 验证结果
        Assertions.assertThat<ReleaseOrderDTO?>(result).isNotNull()
        Assertions.assertThat(result!!.getId()).isEqualTo(testReleaseOrderDO!!.getId())
        Assertions.assertThat(result.getReleaseVersion()).isEqualTo(testReleaseOrderDO!!.getReleaseVersion())
        Assertions.assertThat(result.getAppKey()).isEqualTo(testReleaseOrderDO!!.getAppKey())
        Assertions.assertThat(result.getNamespaceId()).isEqualTo(testReleaseOrderDO!!.getNamespaceId())
        Assertions.assertThat<ReleaseOrderBizType?>(result.getBizType()).isEqualTo(testReleaseOrderDO!!.getBizType())
        Assertions.assertThat(result.getBizId()).isEqualTo(testReleaseOrderDO!!.getBizId())
        Assertions.assertThat<ReleaseType?>(result.getReleaseType()).isEqualTo(testReleaseOrderDO!!.getReleaseType())
        Assertions.assertThat(result.getDescription()).isEqualTo(testReleaseOrderDO!!.getDescription())
        Assertions.assertThat<ReleaseOrderStatus?>(result.getStatus()).isEqualTo(testReleaseOrderDO!!.getStatus())
        Assertions.assertThat(result.getPercent()).isEqualTo(testReleaseOrderDO!!.getPercent())
        Assertions.assertThat(result.getGmtCreate()).isEqualTo(testReleaseOrderDO!!.getGmtCreate())
        Assertions.assertThat(result.getGmtModified()).isEqualTo(testReleaseOrderDO!!.getGmtModified())
        Assertions.assertThat(result.getCreator()).isEqualTo(testReleaseOrderDO!!.getCreator())
        Assertions.assertThat(result.getModifier()).isEqualTo(testReleaseOrderDO!!.getModifier())
    }

    @Test
    fun testToReleaseOrderDTO_WithNull() {
        // 执行转换
        val result = parameterMapper!!.toReleaseOrderDTO(null)

        // 验证结果
        Assertions.assertThat<ReleaseOrderDTO?>(result).isNull()
    }

    @Test
    fun testGetConditionName() {
        // 测试默认条件
        val defaultName = parameterMapper!!.getConditionName(ParameterMapper.DEFAULT_CONDITION_ID, testConditionMap)
        Assertions.assertThat(defaultName).isEqualTo(ParameterMapper.DEFAULT_CONDITION_NAME)

        // 测试普通条件
        val normalName = parameterMapper.getConditionName("condition1", testConditionMap)
        Assertions.assertThat(normalName).isEqualTo("测试条件1")

        // 测试不存在的条件
        val nonExistentName = parameterMapper.getConditionName("non_existent", testConditionMap)
        Assertions.assertThat(nonExistentName).isNull()

        // 测试空条件映射
        val nameWithEmptyMap = parameterMapper.getConditionName("condition1", HashMap<String?, OConditionDO?>())
        Assertions.assertThat(nameWithEmptyMap).isNull()
    }

    @Test
    fun testGetConditionColor() {
        // 测试默认条件
        val defaultColor = parameterMapper!!.getConditionColor(ParameterMapper.DEFAULT_CONDITION_ID, testConditionMap)
        Assertions.assertThat(defaultColor).isNull()

        // 测试普通条件
        val normalColor = parameterMapper.getConditionColor("condition1", testConditionMap)
        Assertions.assertThat(normalColor).isEqualTo("#FF0000")

        // 测试不存在的条件
        val nonExistentColor = parameterMapper.getConditionColor("non_existent", testConditionMap)
        Assertions.assertThat(nonExistentColor).isNull()

        // 测试空条件映射
        val colorWithEmptyMap = parameterMapper.getConditionColor("condition1", HashMap<String?, OConditionDO?>())
        Assertions.assertThat(colorWithEmptyMap).isNull()
    }

    @Test
    fun testMapParameterConditions() {
        // 执行转换
        val result = parameterMapper!!.mapParameterConditions(
            testParameterBO!!.getParameterConditionVersions(), testConditionMap
        )

        // 验证结果
        Assertions.assertThat<ParameterConditionDTO?>(result).hasSize(2)
        Assertions.assertThat<ParameterConditionDTO?>(result)
            .allSatisfy(ThrowingConsumer { dto: ParameterConditionDTO? ->
                Assertions.assertThat(dto!!.getParameterId()).isEqualTo(testParameterBO!!.getParameterId())
                Assertions.assertThat(dto.getReleaseVersion()).isEqualTo("v1.0.0")
                Assertions.assertThat<VersionStatus?>(dto.getStatus()).isEqualTo(VersionStatus.INIT)
            })
    }

    @Test
    fun testMapParameterConditions_WithNull() {
        // 执行转换
        val result = parameterMapper!!.mapParameterConditions(null, testConditionMap)

        // 验证结果
        Assertions.assertThat<ParameterConditionDTO?>(result).isNull()
    }
}
